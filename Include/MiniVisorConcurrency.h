/** @file
  MiniVisor Concurrency Framework Header (Stub Implementation)
  
  This file provides stub definitions for the concurrency framework 
  referenced in the MiniVisor drivers to allow compilation.
  
  Copyright (c) 2024, MiniVisor Project. All rights reserved.
  SPDX-License-Identifier: BSD-2-Clause-Patent
**/

#ifndef __MINI_VISOR_CONCURRENCY_H__
#define __MINI_VISOR_CONCURRENCY_H__

#include <Uefi.h>

//
// Placeholder for future concurrency framework
// Currently provides minimal stub definitions
//

#endif // __MINI_VISOR_CONCURRENCY_H__